// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyCTNZe8EwMU8QaLsqf1I6gt51fbB1X5R4w",
    authDomain: "wavyn-app-default-rtdb.firebaseapp.com",
    projectId: "wavyn-app-default-rtdb",
    storageBucket: "wavyn-app-default-rtdb.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID"
};

// Initialize Firebase
let auth;
try {
    firebase.initializeApp(firebaseConfig);
    auth = firebase.auth();
    console.log('✅ Firebase initialized successfully');
} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    console.log('🔧 Running in DEMO MODE');
}

// DOM elements
const userEmailEl = document.getElementById('userEmail');
const userMenuBtn = document.getElementById('userMenuBtn');
const userMenu = document.getElementById('userMenu');
const signOutBtn = document.getElementById('signOutBtn');
const createRoomBtn = document.getElementById('createRoomBtn');
const joinRoomBtn = document.getElementById('joinRoomBtn');

// Check authentication state
function checkAuth() {
    if (auth) {
        auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('User is signed in:', user.email);
                userEmailEl.textContent = user.email;
            } else {
                console.log('User is signed out, redirecting to login');
                window.location.href = 'index.html';
            }
        });
    } else {
        // Demo mode
        userEmailEl.textContent = '<EMAIL>';
        console.log('🔧 Demo mode - user authentication simulated');
    }
}

// User menu toggle
userMenuBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    userMenu.classList.toggle('hidden');
});

// Close user menu when clicking outside
document.addEventListener('click', () => {
    userMenu.classList.add('hidden');
});

// Sign out functionality
signOutBtn.addEventListener('click', async () => {
    try {
        if (auth) {
            await auth.signOut();
            console.log('User signed out');
        } else {
            console.log('🔧 Demo: User signed out');
        }
        window.location.href = 'index.html';
    } catch (error) {
        console.error('Sign out error:', error);
    }
});

// Create Room functionality
createRoomBtn.addEventListener('click', () => {
    console.log('Create Room clicked');
    // For now, redirect to room with a demo room ID
    const roomId = 'demo-room-' + Math.random().toString(36).substr(2, 9);
    window.location.href = `room.html?id=${roomId}&name=My New Room`;
});

// Join Room functionality
joinRoomBtn.addEventListener('click', () => {
    const roomCode = prompt('Enter room code:');
    if (roomCode) {
        console.log('Joining room with code:', roomCode);
        window.location.href = `room.html?id=${roomCode}&name=Joined Room`;
    }
});

// Room card click handlers
document.addEventListener('DOMContentLoaded', () => {
    // Add click handlers to demo room cards
    const roomCards = document.querySelectorAll('.room-card');
    roomCards.forEach((card, index) => {
        const joinBtn = card.querySelector('button');
        if (joinBtn) {
            joinBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const roomNames = ['Gaming Lounge', 'Study Session', 'Music & Chill'];
                const roomName = roomNames[index] || 'Demo Room';
                const roomId = 'demo-' + roomName.toLowerCase().replace(/\s+/g, '-');
                
                console.log('Joining room:', roomName);
                window.location.href = `room.html?id=${roomId}&name=${encodeURIComponent(roomName)}`;
            });
        }
    });
    
    // Initialize auth check
    checkAuth();
});

// Mobile optimizations
document.addEventListener('DOMContentLoaded', () => {
    // Handle back button for Android (when using Capacitor)
    document.addEventListener('backbutton', (e) => {
        // On dashboard, back button should minimize app or show exit dialog
        e.preventDefault();
        if (confirm('Exit Wavyn?')) {
            if (navigator.app) {
                navigator.app.exitApp();
            }
        }
    });
    
    // Add touch feedback for room cards
    const roomCards = document.querySelectorAll('.room-card');
    roomCards.forEach(card => {
        card.addEventListener('touchstart', () => {
            card.style.transform = 'translateY(0)';
        });
        
        card.addEventListener('touchend', () => {
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
        });
    });
});

// Demo data and functionality
const demoRooms = [
    {
        id: 'gaming-lounge',
        name: 'Gaming Lounge',
        description: 'Casual gaming and chat',
        members: 12,
        speaking: 3,
        status: 'online',
        type: 'public'
    },
    {
        id: 'study-session',
        name: 'Study Session',
        description: 'Focus time with ambient sounds',
        members: 5,
        speaking: 0,
        status: 'busy',
        type: 'private'
    },
    {
        id: 'music-chill',
        name: 'Music & Chill',
        description: 'Share your favorite tracks',
        members: 8,
        speaking: 2,
        status: 'online',
        type: 'public'
    }
];

// Function to render rooms (for future dynamic loading)
function renderRooms(rooms = demoRooms) {
    const roomsList = document.getElementById('roomsList');
    
    roomsList.innerHTML = rooms.map(room => `
        <div class="room-card bg-gray-800/50 border border-gray-700 rounded-xl p-4 cursor-pointer" data-room-id="${room.id}">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-2">
                        <div class="w-3 h-3 status-${room.status} rounded-full"></div>
                        <h4 class="font-medium text-white">${room.name}</h4>
                        <span class="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded-full">${room.type}</span>
                    </div>
                    <p class="text-sm text-gray-400 mb-2">${room.description}</p>
                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                        <span>👥 ${room.members} members</span>
                        <span>${room.speaking > 0 ? `🎤 ${room.speaking} speaking` : '🔇 Silent mode'}</span>
                    </div>
                </div>
                <button class="mobile-btn ${room.type === 'public' ? 'bg-cyan-500 hover:bg-cyan-400 active:bg-cyan-600 text-black' : 'bg-gray-600 hover:bg-gray-500 active:bg-gray-700 text-white'} font-medium px-4 py-2 rounded-lg transition-all duration-200">
                    ${room.type === 'public' ? 'Join' : 'Request'}
                </button>
            </div>
        </div>
    `).join('');
    
    // Re-attach event listeners
    const roomCards = document.querySelectorAll('.room-card');
    roomCards.forEach((card) => {
        const joinBtn = card.querySelector('button');
        const roomId = card.dataset.roomId;
        const room = rooms.find(r => r.id === roomId);
        
        if (joinBtn && room) {
            joinBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('Joining room:', room.name);
                window.location.href = `room.html?id=${room.id}&name=${encodeURIComponent(room.name)}`;
            });
        }
    });
}

// Refresh rooms data (placeholder for future API calls)
function refreshRooms() {
    console.log('Refreshing rooms...');
    // In the future, this would fetch from Firebase or your backend
    renderRooms(demoRooms);
}

// Auto-refresh rooms every 30 seconds
setInterval(refreshRooms, 30000);
