// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyCTNZe8EwMU8QaLsqf1I6gt51fbB1X5R4w",
    authDomain: "wavyn-2d7ed.firebaseapp.com",
    projectId: "wavyn-2d7ed",
    storageBucket: "wavyn-2d7ed.appspot.com",
    messagingSenderId: "************",
    appId: "1:************:web:YOUR_APP_ID"
};

// Initialize Firebase
let auth;
try {
    firebase.initializeApp(firebaseConfig);
    auth = firebase.auth();
    console.log('✅ Firebase initialized successfully');
} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    console.log('🔧 Running in DEMO MODE - authentication will be simulated');
}

// DOM elements
const createAccountModal = document.getElementById('createAccountModal');
const signInModal = document.getElementById('signInModal');
const createAccountForm = document.getElementById('createAccountForm');
const signInForm = document.getElementById('signInForm');

// Mobile-first modal functions
function showModal(modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden'; // Prevent background scroll
}

function hideModal(modal) {
    modal.classList.remove('active');
    document.body.style.overflow = ''; // Restore scroll
}

// Open Create Account Modal
document.getElementById('createAccountBtn').addEventListener('click', () => {
    console.log('Create Account button clicked!');
    showModal(createAccountModal);
});

// Open Sign In Modal  
document.getElementById('signInBtn').addEventListener('click', () => {
    console.log('Sign In button clicked!');
    showModal(signInModal);
});

// Close modals
document.getElementById('closeCreateAccount').addEventListener('click', () => {
    hideModal(createAccountModal);
    clearCreateAccountForm();
});

document.getElementById('closeSignIn').addEventListener('click', () => {
    hideModal(signInModal);
    clearSignInForm();
});

// Switch between modals
document.getElementById('switchToSignIn').addEventListener('click', () => {
    hideModal(createAccountModal);
    clearCreateAccountForm();
    setTimeout(() => showModal(signInModal), 300);
});

document.getElementById('switchToCreateAccount').addEventListener('click', () => {
    hideModal(signInModal);
    clearSignInForm();
    setTimeout(() => showModal(createAccountModal), 300);
});

// Create Account Form Handler
createAccountForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const email = document.getElementById('createEmail').value.trim();
    const password = document.getElementById('createPassword').value;
    const submitBtn = document.getElementById('createAccountSubmit');
    const messageEl = document.getElementById('createAccountMessage');

    // Clear previous messages
    messageEl.classList.add('hidden');

    // Validation
    if (!email || !password) {
        showMessage(messageEl, 'Please fill in all fields', 'error');
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showMessage(messageEl, 'Please enter a valid email address', 'error');
        return;
    }

    // Password validation
    if (password.length < 6) {
        showMessage(messageEl, 'Password must be at least 6 characters long', 'error');
        return;
    }

    // Submit
    submitBtn.disabled = true;
    submitBtn.textContent = 'Creating Account...';

    try {
        if (auth) {
            // Real Firebase
            const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
            showMessage(messageEl, '✅ Account created successfully! Welcome to Wavyn!', 'success');
            console.log('Account created:', userCredential.user);
        } else {
            // Demo mode
            console.log('🔧 DEMO: Creating account for', email);
            showMessage(messageEl, '✅ Demo: Account created! (Firebase not connected)', 'success');
        }
        
        setTimeout(() => {
            hideModal(createAccountModal);
            clearCreateAccountForm();
            // Redirect to dashboard
            window.location.href = 'dashboard.html';
        }, 2000);

    } catch (error) {
        console.error('Create account error:', error);
        showMessage(messageEl, getErrorMessage(error.code), 'error');
    }

    submitBtn.disabled = false;
    submitBtn.textContent = 'Create Account';
});

// Sign In Form Handler
signInForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const email = document.getElementById('signInEmail').value.trim();
    const password = document.getElementById('signInPassword').value;
    const submitBtn = document.getElementById('signInSubmit');
    const messageEl = document.getElementById('signInMessage');

    // Clear previous messages
    messageEl.classList.add('hidden');

    // Validation
    if (!email || !password) {
        showMessage(messageEl, 'Please fill in all fields', 'error');
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showMessage(messageEl, 'Please enter a valid email address', 'error');
        return;
    }

    submitBtn.disabled = true;
    submitBtn.textContent = 'Signing In...';

    try {
        if (auth) {
            // Real Firebase
            const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
            showMessage(messageEl, '✅ Welcome back to Wavyn!', 'success');
            console.log('Signed in:', userCredential.user);
        } else {
            // Demo mode
            console.log('🔧 DEMO: Signing in', email);
            showMessage(messageEl, '✅ Demo: Signed in! (Firebase not connected)', 'success');
        }
        
        setTimeout(() => {
            hideModal(signInModal);
            clearSignInForm();
            // Redirect to dashboard
            window.location.href = 'dashboard.html';
        }, 2000);

    } catch (error) {
        console.error('Sign in error:', error);
        showMessage(messageEl, getErrorMessage(error.code), 'error');
    }

    submitBtn.disabled = false;
    submitBtn.textContent = 'Sign In';
});

// Forgot Password Handler
document.getElementById('forgotPassword').addEventListener('click', async () => {
    const email = document.getElementById('signInEmail').value.trim();
    const messageEl = document.getElementById('signInMessage');
    
    if (!email) {
        showMessage(messageEl, 'Please enter your email address first', 'error');
        return;
    }

    try {
        if (auth) {
            await auth.sendPasswordResetEmail(email);
            showMessage(messageEl, 'Password reset email sent! Check your inbox.', 'success');
        } else {
            showMessage(messageEl, '🔧 Demo: Password reset email would be sent', 'success');
        }
    } catch (error) {
        showMessage(messageEl, getErrorMessage(error.code), 'error');
    }
});

// Helper functions
function clearCreateAccountForm() {
    document.getElementById('createEmail').value = '';
    document.getElementById('createPassword').value = '';
    document.getElementById('createAccountMessage').classList.add('hidden');
}

function clearSignInForm() {
    document.getElementById('signInEmail').value = '';
    document.getElementById('signInPassword').value = '';
    document.getElementById('signInMessage').classList.add('hidden');
}

function showMessage(element, message, type) {
    element.textContent = message;
    element.className = `text-center text-sm p-3 rounded-lg ${type === 'error' ? 'text-red-400 bg-red-900/20 border border-red-800' : 'text-green-400 bg-green-900/20 border border-green-800'}`;
    element.classList.remove('hidden');
}

function getErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/email-already-in-use':
            return 'This email is already registered. Try signing in instead.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address.';
        case 'auth/user-not-found':
            return 'No account found with this email. Create an account first.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again.';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please try again later.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your connection.';
        default:
            return 'An error occurred. Please try again.';
    }
}

// Listen for auth state changes
if (auth) {
    auth.onAuthStateChanged((user) => {
        if (user) {
            console.log('User is signed in:', user.email);
            // Redirect to dashboard if on landing page
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                window.location.href = 'dashboard.html';
            }
        } else {
            console.log('User is signed out');
        }
    });
}

// Mobile-specific optimizations
document.addEventListener('DOMContentLoaded', () => {
    // Initialize modals as hidden
    createAccountModal.classList.remove('active');
    signInModal.classList.remove('active');

    // Prevent zoom on input focus (iOS)
    const inputs = document.querySelectorAll('.mobile-input');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            // Ensure 16px font size to prevent zoom
            input.style.fontSize = '16px';
        });
    });

    // Handle back button for Android (when using Capacitor)
    document.addEventListener('backbutton', (e) => {
        if (createAccountModal.classList.contains('active')) {
            e.preventDefault();
            hideModal(createAccountModal);
            clearCreateAccountForm();
        } else if (signInModal.classList.contains('active')) {
            e.preventDefault();
            hideModal(signInModal);
            clearSignInForm();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (createAccountModal.classList.contains('active')) {
                hideModal(createAccountModal);
                clearCreateAccountForm();
            } else if (signInModal.classList.contains('active')) {
                hideModal(signInModal);
                clearSignInForm();
            }
        }
    });

    // Register Service Worker for PWA
    registerServiceWorker();
});

// Register Service Worker for PWA
async function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        try {
            const registration = await navigator.serviceWorker.register('/service-worker.js');
            console.log('✅ Service Worker registered successfully:', registration.scope);

            // Listen for updates
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // New version available
                        console.log('🔄 New app version available');
                        // You could show a toast here asking user to refresh
                    }
                });
            });

        } catch (error) {
            console.error('❌ Service Worker registration failed:', error);
        }
    }
}
