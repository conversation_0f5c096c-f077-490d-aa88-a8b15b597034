/**
 * 🔥 Firebase Configuration & Initialization
 * Centralized Firebase setup for Wavyn app
 */

export class FirebaseConfig {
    constructor() {
        this.app = null;
        this.auth = null;
        this.isInitialized = false;
        this.isDemoMode = false;
        
        // Firebase configuration
        this.config = {
            apiKey: "AIzaSyCTNZe8EwMU8QaLsqf1I6gt51fbB1X5R4w",
            authDomain: "wavyn-2d7ed.firebaseapp.com",
            projectId: "wavyn-2d7ed",
            storageBucket: "wavyn-2d7ed.appspot.com",
            messagingSenderId: "784655500445",
            appId: "1:784655500445:web:YOUR_APP_ID"
        };
    }

    /**
     * Initialize Firebase services
     */
    async initialize() {
        try {
            console.log('🔥 Initializing Firebase...');
            
            // Check if Firebase is available
            if (typeof firebase === 'undefined') {
                throw new Error('Firebase SDK not loaded');
            }
            
            // Initialize Firebase app
            this.app = firebase.initializeApp(this.config);
            this.auth = firebase.auth();
            
            // Setup auth state listener
            this.setupAuthListener();
            
            this.isInitialized = true;
            console.log('✅ Firebase initialized successfully');
            
            return true;
            
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            console.log('🔧 Running in DEMO MODE');
            
            this.isDemoMode = true;
            this.setupDemoMode();
            
            return false;
        }
    }

    /**
     * Setup authentication state listener
     */
    setupAuthListener() {
        if (!this.auth) return;
        
        this.auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('✅ User authenticated:', user.uid);
                this.onUserAuthenticated(user);
            } else {
                console.log('❌ User not authenticated');
                this.onUserSignedOut();
            }
        });
    }

    /**
     * Setup demo mode fallback
     */
    setupDemoMode() {
        console.log('🎭 Setting up demo mode...');
        
        // Create mock auth object
        this.auth = {
            currentUser: {
                uid: 'demo-user',
                displayName: 'Demo User',
                email: '<EMAIL>'
            },
            onAuthStateChanged: (callback) => {
                // Simulate authenticated user in demo mode
                setTimeout(() => callback(this.auth.currentUser), 100);
            }
        };
    }

    /**
     * Handle user authentication
     */
    onUserAuthenticated(user) {
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('userAuthenticated', {
            detail: { user }
        }));
    }

    /**
     * Handle user sign out
     */
    onUserSignedOut() {
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('userSignedOut'));
        
        // Redirect to login if not in demo mode
        if (!this.isDemoMode) {
            window.location.href = 'index.html';
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.auth?.currentUser || null;
    }

    /**
     * Check if Firebase is initialized
     */
    isReady() {
        return this.isInitialized || this.isDemoMode;
    }

    /**
     * Sign out current user
     */
    async signOut() {
        try {
            if (this.isDemoMode) {
                this.onUserSignedOut();
                return;
            }
            
            await this.auth.signOut();
            console.log('✅ User signed out successfully');
            
        } catch (error) {
            console.error('❌ Sign out failed:', error);
            throw error;
        }
    }
}
