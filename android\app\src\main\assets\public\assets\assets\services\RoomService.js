/**
 * 🏠 Room Service
 * Handles room management, member tracking, and Firebase operations
 */

export class RoomService {
    constructor(auth) {
        this.auth = auth;
        this.currentRoom = null;
        this.members = new Map();
        this.eventListeners = new Map();
        
        // Demo data for fallback
        this.demoMembers = [
            {
                id: 'user1',
                name: '<PERSON>',
                initials: 'J<PERSON>',
                status: 'speaking',
                avatar: 'bg-gradient-to-r from-blue-500 to-purple-500'
            },
            {
                id: 'user2',
                name: '<PERSON>',
                initials: 'AS',
                status: 'listening',
                avatar: 'bg-gradient-to-r from-pink-500 to-red-500'
            },
            {
                id: 'user3',
                name: '<PERSON>',
                initials: 'MB',
                status: 'listening',
                avatar: 'bg-gradient-to-r from-green-500 to-teal-500'
            }
        ];
    }

    /**
     * Join a room
     */
    async joinRoom(roomId, roomName) {
        try {
            console.log(`🏠 Joining room: ${roomId}`);
            
            const user = this.auth?.currentUser;
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Create room data
            const roomData = {
                id: roomId,
                name: roomName || `Room ${roomId}`,
                description: 'Welcome to this voice room! Feel free to join the conversation.',
                createdAt: new Date().toISOString(),
                members: this.demoMembers.length + 1, // Include current user
                isActive: true
            };

            // Add current user to members
            const currentUserMember = {
                id: user.uid,
                name: user.displayName || 'You',
                initials: this.generateInitials(user.displayName || 'You'),
                status: 'muted',
                avatar: 'bg-gradient-to-r from-cyan-500 to-pink-500',
                isCurrentUser: true
            };

            // Update members list
            this.members.clear();
            this.demoMembers.forEach(member => {
                this.members.set(member.id, member);
            });
            this.members.set(user.uid, currentUserMember);

            this.currentRoom = roomData;
            
            // Simulate real-time updates
            this.startMemberUpdates();
            
            // Notify listeners
            this.emit('roomJoined', roomData);
            this.emit('membersUpdate', Array.from(this.members.values()));
            
            return roomData;
            
        } catch (error) {
            console.error('❌ Failed to join room:', error);
            throw error;
        }
    }

    /**
     * Leave the current room
     */
    async leaveRoom(roomId) {
        try {
            console.log(`🚪 Leaving room: ${roomId}`);
            
            // Stop member updates
            this.stopMemberUpdates();
            
            // Clear room data
            this.currentRoom = null;
            this.members.clear();
            
            // Notify listeners
            this.emit('roomLeft', roomId);
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to leave room:', error);
            throw error;
        }
    }

    /**
     * Update user status in room
     */
    async updateUserStatus(status) {
        try {
            const user = this.auth?.currentUser;
            if (!user || !this.members.has(user.uid)) {
                return;
            }

            const member = this.members.get(user.uid);
            member.status = status;
            this.members.set(user.uid, member);

            // Notify listeners
            this.emit('membersUpdate', Array.from(this.members.values()));
            
        } catch (error) {
            console.error('❌ Failed to update user status:', error);
        }
    }

    /**
     * Get current room data
     */
    getCurrentRoom() {
        return this.currentRoom;
    }

    /**
     * Get room members
     */
    getMembers() {
        return Array.from(this.members.values());
    }

    /**
     * Start simulating member updates
     */
    startMemberUpdates() {
        // Simulate speaking status changes
        this.memberUpdateInterval = setInterval(() => {
            if (this.members.size === 0) return;
            
            const members = Array.from(this.members.values());
            const nonCurrentUsers = members.filter(m => !m.isCurrentUser);
            
            if (nonCurrentUsers.length > 0) {
                // Reset all to listening
                nonCurrentUsers.forEach(member => {
                    member.status = 'listening';
                });
                
                // Randomly make someone speak
                if (Math.random() > 0.7) {
                    const randomMember = nonCurrentUsers[Math.floor(Math.random() * nonCurrentUsers.length)];
                    randomMember.status = 'speaking';
                }
                
                // Update members map
                nonCurrentUsers.forEach(member => {
                    this.members.set(member.id, member);
                });
                
                // Notify listeners
                this.emit('membersUpdate', Array.from(this.members.values()));
            }
        }, 3000);
    }

    /**
     * Stop member updates
     */
    stopMemberUpdates() {
        if (this.memberUpdateInterval) {
            clearInterval(this.memberUpdateInterval);
            this.memberUpdateInterval = null;
        }
    }

    /**
     * Generate initials from name
     */
    generateInitials(name) {
        return name
            .split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .slice(0, 2)
            .join('');
    }

    /**
     * Event listener management
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }

    /**
     * Convenience methods for event listeners
     */
    onMembersUpdate(callback) {
        this.on('membersUpdate', callback);
    }

    onRoomUpdate(callback) {
        this.on('roomUpdate', callback);
    }

    onRoomJoined(callback) {
        this.on('roomJoined', callback);
    }

    onRoomLeft(callback) {
        this.on('roomLeft', callback);
    }
}
