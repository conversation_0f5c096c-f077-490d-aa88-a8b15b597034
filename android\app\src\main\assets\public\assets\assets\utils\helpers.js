/**
 * 🛠️ Utility Helpers
 * Common utility functions for the Wavyn app
 */

export class URLHelper {
    /**
     * Parse room URL parameters
     */
    static parseRoomURL() {
        const urlParams = new URLSearchParams(window.location.search);
        
        return {
            roomId: urlParams.get('id') || 'demo-room',
            roomName: urlParams.get('name') || 'Demo Room',
            inviteCode: urlParams.get('invite') || null,
            userId: urlParams.get('user') || null
        };
    }

    /**
     * Generate room URL
     */
    static generateRoomURL(roomId, roomName, baseURL = window.location.origin) {
        const params = new URLSearchParams();
        params.set('id', roomId);
        if (roomName) params.set('name', roomName);
        
        return `${baseURL}/room.html?${params.toString()}`;
    }

    /**
     * Generate invite URL
     */
    static generateInviteURL(roomId, roomName, inviteCode, baseURL = window.location.origin) {
        const params = new URLSearchParams();
        params.set('id', roomId);
        if (roomName) params.set('name', roomName);
        if (inviteCode) params.set('invite', inviteCode);
        
        return `${baseURL}/room.html?${params.toString()}`;
    }
}

export class StorageHelper {
    /**
     * Save data to localStorage with error handling
     */
    static save(key, data) {
        try {
            const serialized = JSON.stringify(data);
            localStorage.setItem(key, serialized);
            return true;
        } catch (error) {
            console.error('❌ Failed to save to localStorage:', error);
            return false;
        }
    }

    /**
     * Load data from localStorage with error handling
     */
    static load(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('❌ Failed to load from localStorage:', error);
            return defaultValue;
        }
    }

    /**
     * Remove data from localStorage
     */
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('❌ Failed to remove from localStorage:', error);
            return false;
        }
    }

    /**
     * Clear all localStorage data
     */
    static clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('❌ Failed to clear localStorage:', error);
            return false;
        }
    }
}

export class ValidationHelper {
    /**
     * Validate room ID
     */
    static isValidRoomId(roomId) {
        if (!roomId || typeof roomId !== 'string') return false;
        
        // Room ID should be alphanumeric with hyphens, 3-50 characters
        const roomIdRegex = /^[a-zA-Z0-9-]{3,50}$/;
        return roomIdRegex.test(roomId);
    }

    /**
     * Validate room name
     */
    static isValidRoomName(roomName) {
        if (!roomName || typeof roomName !== 'string') return false;
        
        // Room name should be 1-100 characters
        return roomName.trim().length >= 1 && roomName.trim().length <= 100;
    }

    /**
     * Validate email
     */
    static isValidEmail(email) {
        if (!email || typeof email !== 'string') return false;
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate display name
     */
    static isValidDisplayName(name) {
        if (!name || typeof name !== 'string') return false;
        
        // Display name should be 1-50 characters
        return name.trim().length >= 1 && name.trim().length <= 50;
    }
}

export class FormatHelper {
    /**
     * Format timestamp to readable string
     */
    static formatTimestamp(timestamp) {
        try {
            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            
            return date.toLocaleDateString();
        } catch (error) {
            return 'Unknown';
        }
    }

    /**
     * Format duration in seconds to readable string
     */
    static formatDuration(seconds) {
        if (seconds < 60) return `${seconds}s`;
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        if (minutes < 60) {
            return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
        }
        
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        
        return `${hours}h ${remainingMinutes}m`;
    }

    /**
     * Truncate text to specified length
     */
    static truncateText(text, maxLength = 50) {
        if (!text || typeof text !== 'string') return '';
        
        if (text.length <= maxLength) return text;
        
        return text.substring(0, maxLength - 3) + '...';
    }

    /**
     * Generate random ID
     */
    static generateId(prefix = '', length = 8) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = prefix;
        
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return result;
    }
}

export class DeviceHelper {
    /**
     * Check if device is mobile
     */
    static isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * Check if device is iOS
     */
    static isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * Check if device is Android
     */
    static isAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    /**
     * Check if running in Capacitor
     */
    static isCapacitor() {
        return window.Capacitor !== undefined;
    }

    /**
     * Get device info
     */
    static getDeviceInfo() {
        return {
            isMobile: this.isMobile(),
            isIOS: this.isIOS(),
            isAndroid: this.isAndroid(),
            isCapacitor: this.isCapacitor(),
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language
        };
    }
}

export class AudioHelper {
    /**
     * Check if Web Audio API is supported
     */
    static isWebAudioSupported() {
        return !!(window.AudioContext || window.webkitAudioContext);
    }

    /**
     * Check if getUserMedia is supported
     */
    static isGetUserMediaSupported() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    }

    /**
     * Check if WebRTC is supported
     */
    static isWebRTCSupported() {
        return !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection);
    }

    /**
     * Get audio capabilities
     */
    static getAudioCapabilities() {
        return {
            webAudio: this.isWebAudioSupported(),
            getUserMedia: this.isGetUserMediaSupported(),
            webRTC: this.isWebRTCSupported()
        };
    }
}

export class ErrorHelper {
    /**
     * Create user-friendly error message
     */
    static getUserFriendlyMessage(error) {
        if (!error) return 'An unknown error occurred';
        
        const message = error.message || error.toString();
        
        // Common error patterns
        if (message.includes('permission')) {
            return 'Permission denied. Please allow access and try again.';
        }
        
        if (message.includes('network') || message.includes('fetch')) {
            return 'Network error. Please check your connection.';
        }
        
        if (message.includes('auth') || message.includes('unauthorized')) {
            return 'Authentication failed. Please sign in again.';
        }
        
        if (message.includes('not found')) {
            return 'The requested resource was not found.';
        }
        
        return message;
    }

    /**
     * Log error with context
     */
    static logError(error, context = '') {
        const timestamp = new Date().toISOString();
        const errorInfo = {
            timestamp,
            context,
            message: error.message || error.toString(),
            stack: error.stack,
            userAgent: navigator.userAgent
        };
        
        console.error('❌ Error logged:', errorInfo);
        
        // In production, you might want to send this to an error tracking service
        return errorInfo;
    }
}
