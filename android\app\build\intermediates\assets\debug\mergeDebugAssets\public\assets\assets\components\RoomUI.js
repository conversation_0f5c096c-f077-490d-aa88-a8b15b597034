/**
 * 🎨 Room UI Component
 * Handles all DOM interactions and UI updates for the room
 */

export class RoomUI {
    constructor() {
        this.elements = {};
        this.eventCallbacks = {};
        this.isMenuOpen = false;
        
        // Initialize DOM elements
        this.initializeElements();
        this.setupEventListeners();
        
        console.log('🎨 Room UI initialized');
    }

    /**
     * Initialize DOM element references
     */
    initializeElements() {
        this.elements = {
            // Header elements
            roomName: document.getElementById('roomName'),
            roomStatus: document.getElementById('roomStatus'),
            backBtn: document.getElementById('backBtn'),
            roomMenuBtn: document.getElementById('roomMenuBtn'),
            roomMenu: document.getElementById('roomMenu'),
            
            // Content elements
            membersList: document.getElementById('membersList'),
            roomDescription: document.getElementById('roomDescription'),
            yourStatus: document.getElementById('yourStatus'),
            
            // Control elements
            muteBtn: document.getElementById('muteBtn'),
            leaveBtn: document.getElementById('leaveBtn'),
            settingsBtn: document.getElementById('settingsBtn')
        };

        // Validate required elements
        const requiredElements = ['roomName', 'roomStatus', 'membersList', 'muteBtn'];
        requiredElements.forEach(elementId => {
            if (!this.elements[elementId]) {
                console.warn(`⚠️ Required element not found: ${elementId}`);
            }
        });
    }

    /**
     * Setup DOM event listeners
     */
    setupEventListeners() {
        // Back button
        if (this.elements.backBtn) {
            this.elements.backBtn.addEventListener('click', () => {
                this.eventCallbacks.onBackClick?.();
            });
        }

        // Room menu button
        if (this.elements.roomMenuBtn) {
            this.elements.roomMenuBtn.addEventListener('click', () => {
                this.eventCallbacks.onMenuToggle?.();
            });
        }

        // Mute button
        if (this.elements.muteBtn) {
            this.elements.muteBtn.addEventListener('click', () => {
                const isMuted = this.elements.muteBtn.classList.contains('muted');
                this.eventCallbacks.onMuteToggle?.(!isMuted);
            });
        }

        // Leave button
        if (this.elements.leaveBtn) {
            this.elements.leaveBtn.addEventListener('click', () => {
                this.eventCallbacks.onLeaveRoom?.();
            });
        }

        // Settings button
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => {
                this.eventCallbacks.onSettingsClick?.();
            });
        }

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isMenuOpen && 
                !this.elements.roomMenu?.contains(e.target) && 
                !this.elements.roomMenuBtn?.contains(e.target)) {
                this.closeMenu();
            }
        });
    }

    /**
     * Update room information
     */
    updateRoomInfo(roomData) {
        if (this.elements.roomName) {
            this.elements.roomName.textContent = roomData.name || 'Unknown Room';
        }

        if (this.elements.roomDescription && roomData.description) {
            this.elements.roomDescription.textContent = roomData.description;
        }

        console.log('🏠 Room info updated:', roomData.name);
    }

    /**
     * Update room status
     */
    updateRoomStatus(status) {
        if (this.elements.roomStatus) {
            this.elements.roomStatus.textContent = status;
        }

        console.log('📊 Room status updated:', status);
    }

    /**
     * Update members list
     */
    updateMembers(members) {
        if (!this.elements.membersList) return;

        // Clear existing members
        this.elements.membersList.innerHTML = '';

        // Add each member
        members.forEach(member => {
            const memberElement = this.createMemberElement(member);
            this.elements.membersList.appendChild(memberElement);
        });

        console.log('👥 Members updated:', members.length);
    }

    /**
     * Create member element
     */
    createMemberElement(member) {
        const memberDiv = document.createElement('div');
        memberDiv.className = 'flex flex-col items-center space-y-2';
        memberDiv.setAttribute('data-member-id', member.id);

        // Avatar
        const avatar = document.createElement('div');
        avatar.className = `user-avatar ${member.avatar} text-white ${member.status === 'speaking' ? 'user-speaking' : ''}`;
        avatar.textContent = member.initials;

        // Info container
        const infoDiv = document.createElement('div');
        infoDiv.className = 'text-center';

        // Name
        const name = document.createElement('p');
        name.className = 'text-sm font-medium text-white';
        name.textContent = member.name;

        // Status
        const status = document.createElement('p');
        status.className = `text-xs ${this.getStatusColor(member.status)}`;
        status.textContent = this.getStatusText(member.status);

        // Assemble element
        infoDiv.appendChild(name);
        infoDiv.appendChild(status);
        memberDiv.appendChild(avatar);
        memberDiv.appendChild(infoDiv);

        return memberDiv;
    }

    /**
     * Get status color class
     */
    getStatusColor(status) {
        switch (status) {
            case 'speaking': return 'text-green-400';
            case 'muted': return 'text-red-400';
            case 'listening': return 'text-gray-400';
            default: return 'text-gray-400';
        }
    }

    /**
     * Get status text
     */
    getStatusText(status) {
        switch (status) {
            case 'speaking': return 'Speaking';
            case 'muted': return 'Muted';
            case 'listening': return 'Listening';
            default: return 'Unknown';
        }
    }

    /**
     * Update mute status
     */
    updateMuteStatus(isMuted) {
        if (!this.elements.muteBtn) return;

        if (isMuted) {
            this.elements.muteBtn.classList.remove('unmuted');
            this.elements.muteBtn.classList.add('muted');
        } else {
            this.elements.muteBtn.classList.remove('muted');
            this.elements.muteBtn.classList.add('unmuted');
        }

        // Update your status text
        if (this.elements.yourStatus) {
            this.elements.yourStatus.textContent = isMuted ? 'Muted' : 'Unmuted';
            this.elements.yourStatus.className = `text-xs ${isMuted ? 'text-red-400' : 'text-green-400'}`;
        }

        console.log('🎤 Mute status updated:', isMuted ? 'Muted' : 'Unmuted');
    }

    /**
     * Toggle menu visibility
     */
    toggleMenu() {
        if (this.isMenuOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    /**
     * Open menu
     */
    openMenu() {
        if (this.elements.roomMenu) {
            this.elements.roomMenu.classList.remove('hidden');
            this.isMenuOpen = true;
        }
    }

    /**
     * Close menu
     */
    closeMenu() {
        if (this.elements.roomMenu) {
            this.elements.roomMenu.classList.add('hidden');
            this.isMenuOpen = false;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create error toast
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-20 left-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);

        console.error('❌ Error shown to user:', message);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        // Create success toast
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-20 left-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
        successDiv.textContent = message;

        document.body.appendChild(successDiv);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);

        console.log('✅ Success shown to user:', message);
    }

    /**
     * Event callback setters
     */
    onBackClick(callback) {
        this.eventCallbacks.onBackClick = callback;
    }

    onMuteToggle(callback) {
        this.eventCallbacks.onMuteToggle = callback;
    }

    onLeaveRoom(callback) {
        this.eventCallbacks.onLeaveRoom = callback;
    }

    onSettingsClick(callback) {
        this.eventCallbacks.onSettingsClick = callback;
    }

    onMenuToggle(callback) {
        this.eventCallbacks.onMenuToggle = callback;
    }
}
