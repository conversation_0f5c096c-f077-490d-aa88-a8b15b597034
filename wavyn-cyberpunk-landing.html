<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="Wavyn - AI-powered voice rooms for creators, gamers, and teams. Next-generation communication platform.">
    <meta name="keywords" content="voice chat, AI, gaming, creators, teams, communication">
    <title>Wavyn - AI-Powered Voice Rooms</title>
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/10.0.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.0.0/firebase-auth.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700;800;900&display=swap');

        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow: hidden;
        }
        
        /* Custom cyberpunk animations */
        @keyframes shine-sweep {
            0% {
                transform: translateX(-100%) skewX(-20deg);
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            15% {
                transform: translateX(400%) skewX(-20deg);
                opacity: 0;
            }
            100% {
                transform: translateX(400%) skewX(-20deg);
                opacity: 0;
            }
        }


        @keyframes float-particle {
            0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
            50% { opacity: 1; transform: translateY(-20px) translateX(15px) rotate(180deg); }
            100% { transform: translateY(-40px) translateX(30px) rotate(360deg); opacity: 0; }
        }
        
        @keyframes subtle-glow {
            0%, 100% { box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3); }
            50% { box-shadow: 0 4px 25px rgba(255, 0, 128, 0.4); }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            0% {
                opacity: 0;
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideInLeft {
            0% {
                opacity: 0;
                transform: translateX(-50px) rotate(-15deg) perspective(1000px) rotateX(20deg) rotateY(-25deg);
            }
            100% {
                opacity: 0.06;
                transform: translateX(0) rotate(-15deg) perspective(1000px) rotateX(20deg) rotateY(-25deg);
            }
        }
        /* Font families */
        .font-modern { font-family: 'Inter', sans-serif; }
        .font-mono { font-family: 'JetBrains Mono', monospace; }
        .font-cyber { font-family: 'Exo 2', 'Orbitron', sans-serif; }

        /* Background */
        .bg-minimal {
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 30%, #16213e 70%, #0f1419 100%);
        }

        /* Animations */
        .particle { animation: float-particle linear infinite; }
        .subtle-glow { animation: subtle-glow 4s ease-in-out infinite; }

        /* Entrance animations */
        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-fadeInScale {
            animation: fadeInScale 0.6s ease-out forwards;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.7s ease-out forwards;
        }

        /* Interactive cursor effects */
        .cursor-interactive {
            transition: all 0.3s ease;
        }

        .cursor-interactive:hover {
            transform: translateY(-2px);
        }

        /* Initial hidden state */
        .initial-hidden {
            opacity: 0;
        }

        /* Mobile-first modal styles */
        .modal-slide-in {
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
        }

        .modal-slide-in.active {
            transform: translateX(0);
        }

        /* Mobile input styles */
        .mobile-input {
            font-size: 16px; /* Prevents zoom on iOS */
            -webkit-appearance: none;
            appearance: none;
        }

        /* Touch-friendly buttons */
        .mobile-btn {
            min-height: 56px;
            touch-action: manipulation;
        }

        /* Title styling */
        .cyber-title {
            background: linear-gradient(135deg, #ffffff 0%, #00d4ff 40%, #ff0080 80%, #ffffff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 900;
            letter-spacing: 0.1em;
            position: relative;
        }

        .cyber-title::after {
            content: 'WAVYN';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                transparent 30%,
                rgba(0, 255, 255, 0.9) 50%,
                transparent 70%,
                transparent 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine-sweep 6s ease-in-out infinite;
            z-index: 1;
            font-family: 'Exo 2', 'Orbitron', sans-serif;
            font-weight: 900;
            letter-spacing: 0.1em;
        }

        .cyber-title::before {
            content: 'WAVYN';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff0080 0%, #8000ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            z-index: -1;
            transform: translate(1px, 1px);
            opacity: 0.3;
        }

        .planet-container {
            position: fixed;
            top: 20%;
            right: -200px;
            width: 600px;
            height: 600px;
            opacity: 0.06;
            pointer-events: none;
            z-index: 1;
            transform: rotate(-15deg) perspective(1000px) rotateX(20deg) rotateY(-25deg);
        }

        .planet-container.initial-hidden {
            opacity: 0;
        }

        .planet-container.animate-slideInLeft {
            animation: slideInLeft 0.7s ease-out forwards;
        }

        .planet {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background:
                /* Dark space-like appearance */
                radial-gradient(circle at 35% 25%, #001122 0%, #000811 40%, #000408 100%),
                radial-gradient(ellipse 200px 140px at 30% 45%, #00ff88 0%, transparent 65%),
                radial-gradient(ellipse 160px 110px at 75% 35%, #ff0080 0%, transparent 55%),
                radial-gradient(ellipse 140px 90px at 65% 75%, #00d4ff 0%, transparent 60%),
                radial-gradient(ellipse 100px 60px at 15% 80%, #8000ff 0%, transparent 50%);
            box-shadow:
                inset -60px -60px 120px rgba(255, 255, 255, 0.7),
                inset 30px 30px 60px rgba(0, 255, 255, 0.1),
                0 0 100px rgba(255, 0, 128, 0.08);
        }

    </style>
</head>
<body class="bg-minimal h-screen overflow-hidden relative font-modern m-0 p-0">


    <!-- 3D Planet Earth -->
    <div class="planet-container initial-hidden animate-slideInLeft" style="animation-delay: 0.3s;">
        <div class="planet"></div>
    </div>

    <!-- Minimal floating elements -->
    <div class="fixed inset-0 pointer-events-none">
        <div class="particle absolute" style="left: 20%; top: 30%; animation-duration: 20s; animation-delay: 2s;">
            <div class="w-2 h-2 bg-cyan-400 rounded-full opacity-40"></div>
        </div>
        <div class="particle absolute" style="left: 70%; top: 60%; animation-duration: 25s; animation-delay: 4s;">
            <div class="w-1 h-1 bg-pink-400 rounded-full opacity-50"></div>
        </div>
        <div class="particle absolute" style="left: 15%; top: 70%; animation-duration: 30s; animation-delay: 6s;">
            <div class="w-1.5 h-1.5 bg-purple-400 rounded-full opacity-35"></div>
        </div>
        <div class="particle absolute" style="left: 85%; top: 25%; animation-duration: 22s; animation-delay: 8s;">
            <div class="w-1 h-1 bg-emerald-400 rounded-full opacity-45"></div>
        </div>
    </div>

    <!-- Main content -->
    <div class="fixed inset-0 z-10 flex flex-col items-center justify-center px-6">

        <!-- Centered content -->
        <div class="text-center max-w-lg mx-auto">
            <!-- Logo -->
            <h1 class="text-5xl sm:text-6xl cyber-title mb-6 font-cyber initial-hidden animate-fadeInScale cursor-interactive" style="animation-delay: 0.2s;">
                WAVYN
            </h1>

            <!-- Subheading -->
            <p class="text-lg text-gray-200 mb-8 font-light leading-relaxed initial-hidden animate-fadeInUp cursor-interactive" style="animation-delay: 0.5s;">
                AI-powered voice rooms for creators, gamers, and teams
            </p>

            <!-- Actions -->
            <div class="space-y-4 initial-hidden animate-fadeInUp" style="animation-delay: 0.8s;">
                <button id="createAccountBtn" class="mobile-btn subtle-glow w-full bg-gradient-to-r from-cyan-500 to-pink-500 hover:from-cyan-400 hover:to-pink-400 active:from-cyan-600 active:to-pink-600 text-black font-semibold py-4 px-8 rounded-xl text-lg transition-all duration-200 transform active:scale-95 focus:outline-none focus:ring-4 focus:ring-cyan-400 focus:ring-opacity-30">
                    Create Account
                </button>

                <div class="text-center">
                    <button id="signInBtn" class="text-gray-300 hover:text-cyan-300 active:text-cyan-400 transition-all duration-200 text-base font-medium py-2 px-4 rounded-lg">
                        Already have an account? <span class="text-pink-400 hover:text-pink-300">Sign In</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Account Modal - Mobile-First -->
    <div id="createAccountModal" class="fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 z-50 modal-slide-in">
        <div class="h-full flex flex-col safe-area-inset">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 pt-safe border-b border-gray-700">
                <button id="closeCreateAccount" class="p-3 rounded-full hover:bg-gray-700 active:bg-gray-600 transition-colors duration-200">
                    <svg class="w-6 h-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <h1 class="text-xl font-semibold text-white">Create Account</h1>
                <div class="w-12"></div>
            </div>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-6 pb-safe">
                    <!-- Welcome Text -->
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-white mb-2">Join Wavyn</h2>
                        <p class="text-gray-400 text-base">Create your account to get started</p>
                    </div>

                    <!-- Form -->
                    <form id="createAccountForm" class="space-y-6 max-w-sm mx-auto">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <input type="email" id="createEmail" placeholder="Enter your email" required
                                   class="mobile-input w-full px-4 py-4 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                            <input type="password" id="createPassword" placeholder="Create a password" required
                                   class="mobile-input w-full px-4 py-4 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200">
                            <p class="text-xs text-gray-500 mt-2">Must be at least 6 characters</p>
                        </div>

                        <div id="createAccountMessage" class="hidden text-center text-sm p-3 rounded-lg"></div>

                        <button type="submit" id="createAccountSubmit"
                                class="mobile-btn w-full bg-gradient-to-r from-cyan-500 to-pink-500 hover:from-cyan-400 hover:to-pink-400 active:from-cyan-600 active:to-pink-600 text-black font-semibold py-4 px-6 rounded-xl text-lg transition-all duration-200 transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed">
                            Create Account
                        </button>
                    </form>

                    <!-- Sign In Link -->
                    <div class="text-center mt-8 max-w-sm mx-auto">
                        <p class="text-gray-400 mb-2">Already have an account?</p>
                        <button id="switchToSignIn" class="text-cyan-400 font-medium text-base py-2 px-4 rounded-lg hover:text-cyan-300 hover:bg-gray-800 active:bg-gray-700 transition-all duration-200">
                            Sign In Instead
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sign In Modal - Mobile-First -->
    <div id="signInModal" class="fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 z-50 modal-slide-in">
        <div class="h-full flex flex-col safe-area-inset">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 pt-safe border-b border-gray-700">
                <button id="closeSignIn" class="p-3 rounded-full hover:bg-gray-700 active:bg-gray-600 transition-colors duration-200">
                    <svg class="w-6 h-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <h1 class="text-xl font-semibold text-white">Sign In</h1>
                <div class="w-12"></div>
            </div>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-6 pb-safe">
                    <!-- Welcome Text -->
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-white mb-2">Welcome Back</h2>
                        <p class="text-gray-400 text-base">Sign in to your Wavyn account</p>
                    </div>

                    <!-- Form -->
                    <form id="signInForm" class="space-y-6 max-w-sm mx-auto">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <input type="email" id="signInEmail" placeholder="Enter your email" required
                                   class="mobile-input w-full px-4 py-4 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                            <input type="password" id="signInPassword" placeholder="Enter your password" required
                                   class="mobile-input w-full px-4 py-4 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200">
                        </div>

                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input type="checkbox" id="rememberMe" class="w-5 h-5 text-cyan-400 bg-gray-800 border-gray-600 rounded focus:ring-cyan-400 focus:ring-2">
                                <span class="ml-3 text-sm text-gray-300">Remember me</span>
                            </label>
                            <button type="button" id="forgotPassword" class="text-sm text-cyan-400 hover:text-cyan-300 py-2 px-3 rounded transition-colors duration-200">
                                Forgot password?
                            </button>
                        </div>

                        <div id="signInMessage" class="hidden text-center text-sm p-3 rounded-lg"></div>

                        <button type="submit" id="signInSubmit"
                                class="mobile-btn w-full bg-gradient-to-r from-cyan-500 to-pink-500 hover:from-cyan-400 hover:to-pink-400 active:from-cyan-600 active:to-pink-600 text-black font-semibold py-4 px-6 rounded-xl text-lg transition-all duration-200 transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed">
                            Sign In
                        </button>
                    </form>

                    <!-- Create Account Link -->
                    <div class="text-center mt-8 max-w-sm mx-auto">
                        <p class="text-gray-400 mb-2">Don't have an account?</p>
                        <button id="switchToCreateAccount" class="text-cyan-400 font-medium text-base py-2 px-4 rounded-lg hover:text-cyan-300 hover:bg-gray-800 active:bg-gray-700 transition-all duration-200">
                            Create Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase Configuration and Mobile Authentication Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCTNZe8EwMU8QaLsqf1I6gt51fbB1X5R4w",
            authDomain: "wavyn-2d7ed.firebaseapp.com",
            projectId: "wavyn-2d7ed",
            storageBucket: "wavyn-2d7ed.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:YOUR_APP_ID"
        };

        // Initialize Firebase
        let auth;
        try {
            firebase.initializeApp(firebaseConfig);
            auth = firebase.auth();
            console.log('✅ Firebase initialized successfully');
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            console.log('🔧 Running in DEMO MODE - authentication will be simulated');
        }

        // DOM elements
        const createAccountModal = document.getElementById('createAccountModal');
        const signInModal = document.getElementById('signInModal');
        const createAccountForm = document.getElementById('createAccountForm');
        const signInForm = document.getElementById('signInForm');

        // Mobile-first modal functions
        function showModal(modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent background scroll
        }

        function hideModal(modal) {
            modal.classList.remove('active');
            document.body.style.overflow = ''; // Restore scroll
        }

        // Open Create Account Modal
        document.getElementById('createAccountBtn').addEventListener('click', () => {
            console.log('Create Account button clicked!');
            showModal(createAccountModal);
        });

        // Open Sign In Modal
        document.getElementById('signInBtn').addEventListener('click', () => {
            console.log('Sign In button clicked!');
            showModal(signInModal);
        });

        // Close modals
        document.getElementById('closeCreateAccount').addEventListener('click', () => {
            hideModal(createAccountModal);
            clearCreateAccountForm();
        });

        document.getElementById('closeSignIn').addEventListener('click', () => {
            hideModal(signInModal);
            clearSignInForm();
        });

        // Switch between modals
        document.getElementById('switchToSignIn').addEventListener('click', () => {
            hideModal(createAccountModal);
            clearCreateAccountForm();
            setTimeout(() => showModal(signInModal), 300);
        });

        document.getElementById('switchToCreateAccount').addEventListener('click', () => {
            hideModal(signInModal);
            clearSignInForm();
            setTimeout(() => showModal(createAccountModal), 300);
        });

        // Create Account Form Handler
        createAccountForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('createEmail').value.trim();
            const password = document.getElementById('createPassword').value;
            const submitBtn = document.getElementById('createAccountSubmit');
            const messageEl = document.getElementById('createAccountMessage');

            // Clear previous messages
            messageEl.classList.add('hidden');

            // Validation
            if (!email || !password) {
                showMessage(messageEl, 'Please fill in all fields', 'error');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage(messageEl, 'Please enter a valid email address', 'error');
                return;
            }

            // Password validation
            if (password.length < 6) {
                showMessage(messageEl, 'Password must be at least 6 characters long', 'error');
                return;
            }

            // Submit
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Account...';

            try {
                if (auth) {
                    // Real Firebase
                    const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
                    showMessage(messageEl, '✅ Account created successfully! Welcome to Wavyn!', 'success');
                    console.log('Account created:', userCredential.user);
                } else {
                    // Demo mode
                    console.log('🔧 DEMO: Creating account for', email);
                    showMessage(messageEl, '✅ Demo: Account created! (Firebase not connected)', 'success');
                }

                setTimeout(() => {
                    hideModal(createAccountModal);
                    clearCreateAccountForm();
                }, 2000);

            } catch (error) {
                console.error('Create account error:', error);
                showMessage(messageEl, getErrorMessage(error.code), 'error');
            }

            submitBtn.disabled = false;
            submitBtn.textContent = 'Create Account';
        });

        // Sign In Form Handler
        signInForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;
            const submitBtn = document.getElementById('signInSubmit');
            const messageEl = document.getElementById('signInMessage');

            // Clear previous messages
            messageEl.classList.add('hidden');

            // Validation
            if (!email || !password) {
                showMessage(messageEl, 'Please fill in all fields', 'error');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage(messageEl, 'Please enter a valid email address', 'error');
                return;
            }

            submitBtn.disabled = true;
            submitBtn.textContent = 'Signing In...';

            try {
                if (auth) {
                    // Real Firebase
                    const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
                    showMessage(messageEl, '✅ Welcome back to Wavyn!', 'success');
                    console.log('Signed in:', userCredential.user);
                } else {
                    // Demo mode
                    console.log('🔧 DEMO: Signing in', email);
                    showMessage(messageEl, '✅ Demo: Signed in! (Firebase not connected)', 'success');
                }

                setTimeout(() => {
                    hideModal(signInModal);
                    clearSignInForm();
                }, 2000);

            } catch (error) {
                console.error('Sign in error:', error);
                showMessage(messageEl, getErrorMessage(error.code), 'error');
            }

            submitBtn.disabled = false;
            submitBtn.textContent = 'Sign In';
        });

        // Forgot Password Handler
        document.getElementById('forgotPassword').addEventListener('click', async () => {
            const email = document.getElementById('signInEmail').value.trim();
            const messageEl = document.getElementById('signInMessage');

            if (!email) {
                showMessage(messageEl, 'Please enter your email address first', 'error');
                return;
            }

            try {
                await auth.sendPasswordResetEmail(email);
                showMessage(messageEl, 'Password reset email sent! Check your inbox.', 'success');
            } catch (error) {
                showMessage(messageEl, getErrorMessage(error.code), 'error');
            }
        });

        // Helper functions
        function clearCreateAccountForm() {
            document.getElementById('createEmail').value = '';
            document.getElementById('createPassword').value = '';
            document.getElementById('createAccountMessage').classList.add('hidden');
        }

        function clearSignInForm() {
            document.getElementById('signInEmail').value = '';
            document.getElementById('signInPassword').value = '';
            document.getElementById('signInMessage').classList.add('hidden');
        }

        function showMessage(element, message, type) {
            element.textContent = message;
            element.className = `text-center text-sm p-3 rounded-lg ${type === 'error' ? 'text-red-400 bg-red-900/20 border border-red-800' : 'text-green-400 bg-green-900/20 border border-green-800'}`;
            element.classList.remove('hidden');
        }

        function getErrorMessage(errorCode) {
            switch (errorCode) {
                case 'auth/email-already-in-use':
                    return 'This email is already registered. Try signing in instead.';
                case 'auth/weak-password':
                    return 'Password should be at least 6 characters long.';
                case 'auth/invalid-email':
                    return 'Please enter a valid email address.';
                case 'auth/user-not-found':
                    return 'No account found with this email. Create an account first.';
                case 'auth/wrong-password':
                    return 'Incorrect password. Please try again.';
                case 'auth/too-many-requests':
                    return 'Too many failed attempts. Please try again later.';
                case 'auth/network-request-failed':
                    return 'Network error. Please check your connection.';
                default:
                    return 'An error occurred. Please try again.';
            }
        }

        // Listen for auth state changes
        auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('User is signed in:', user.email);
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
            } else {
                console.log('User is signed out');
            }
        });

        // Mobile-specific optimizations
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize modals as hidden
            createAccountModal.classList.remove('active');
            signInModal.classList.remove('active');

            // Prevent zoom on input focus (iOS)
            const inputs = document.querySelectorAll('.mobile-input');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    // Ensure 16px font size to prevent zoom
                    input.style.fontSize = '16px';
                });
            });

            // Handle back button for Android (when using Capacitor)
            document.addEventListener('backbutton', (e) => {
                if (createAccountModal.classList.contains('active')) {
                    e.preventDefault();
                    hideModal(createAccountModal);
                    clearCreateAccountForm();
                } else if (signInModal.classList.contains('active')) {
                    e.preventDefault();
                    hideModal(signInModal);
                    clearSignInForm();
                }
            });

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    if (createAccountModal.classList.contains('active')) {
                        hideModal(createAccountModal);
                        clearCreateAccountForm();
                    } else if (signInModal.classList.contains('active')) {
                        hideModal(signInModal);
                        clearSignInForm();
                    }
                }
            });
        });
    </script>
</body>
</html>
