/**
 * 👤 User Service
 * Handles user management, presence, and profile data
 */

export class UserService {
    constructor(auth) {
        this.auth = auth;
        this.currentUser = null;
        this.userProfile = null;
        this.presenceStatus = 'offline';
        this.eventListeners = new Map();
    }

    /**
     * Initialize user service
     */
    async initialize() {
        try {
            console.log('👤 Initializing user service...');
            
            // Get current user
            this.currentUser = this.auth?.currentUser;
            
            if (this.currentUser) {
                await this.loadUserProfile();
                await this.setPresenceStatus('online');
            }
            
            console.log('✅ User service initialized');
            return true;
            
        } catch (error) {
            console.error('❌ User service initialization failed:', error);
            return false;
        }
    }

    /**
     * Load user profile data
     */
    async loadUserProfile() {
        try {
            if (!this.currentUser) {
                throw new Error('No authenticated user');
            }

            // Create user profile from auth data
            this.userProfile = {
                uid: this.currentUser.uid,
                displayName: this.currentUser.displayName || 'Anonymous User',
                email: this.currentUser.email || '',
                photoURL: this.currentUser.photoURL || null,
                initials: this.generateInitials(this.currentUser.displayName || 'Anonymous User'),
                joinedAt: new Date().toISOString(),
                lastSeen: new Date().toISOString(),
                preferences: {
                    theme: 'dark',
                    notifications: true,
                    autoMute: false
                }
            };

            console.log('✅ User profile loaded:', this.userProfile.displayName);
            
            // Emit profile loaded event
            this.emit('profileLoaded', this.userProfile);
            
            return this.userProfile;
            
        } catch (error) {
            console.error('❌ Failed to load user profile:', error);
            throw error;
        }
    }

    /**
     * Update user profile
     */
    async updateProfile(updates) {
        try {
            if (!this.userProfile) {
                throw new Error('User profile not loaded');
            }

            // Merge updates
            this.userProfile = {
                ...this.userProfile,
                ...updates,
                lastUpdated: new Date().toISOString()
            };

            console.log('✅ User profile updated');
            
            // Emit profile updated event
            this.emit('profileUpdated', this.userProfile);
            
            return this.userProfile;
            
        } catch (error) {
            console.error('❌ Failed to update user profile:', error);
            throw error;
        }
    }

    /**
     * Set user presence status
     */
    async setPresenceStatus(status) {
        try {
            const validStatuses = ['online', 'away', 'busy', 'offline'];
            
            if (!validStatuses.includes(status)) {
                throw new Error(`Invalid status: ${status}`);
            }

            this.presenceStatus = status;
            
            if (this.userProfile) {
                this.userProfile.lastSeen = new Date().toISOString();
                this.userProfile.status = status;
            }

            console.log(`👤 Presence status set to: ${status}`);
            
            // Emit presence change event
            this.emit('presenceChange', {
                status: this.presenceStatus,
                timestamp: new Date().toISOString()
            });
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to set presence status:', error);
            throw error;
        }
    }

    /**
     * Get current user data
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Get user profile
     */
    getUserProfile() {
        return this.userProfile;
    }

    /**
     * Get presence status
     */
    getPresenceStatus() {
        return this.presenceStatus;
    }

    /**
     * Generate user initials
     */
    generateInitials(name) {
        if (!name || typeof name !== 'string') {
            return 'AU'; // Anonymous User
        }
        
        return name
            .trim()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .slice(0, 2)
            .join('');
    }

    /**
     * Get user avatar data
     */
    getUserAvatar() {
        if (!this.userProfile) {
            return {
                initials: 'AU',
                photoURL: null,
                backgroundColor: 'bg-gradient-to-r from-gray-500 to-gray-600'
            };
        }

        return {
            initials: this.userProfile.initials,
            photoURL: this.userProfile.photoURL,
            backgroundColor: this.generateAvatarColor(this.userProfile.uid)
        };
    }

    /**
     * Generate consistent avatar color based on user ID
     */
    generateAvatarColor(uid) {
        const colors = [
            'bg-gradient-to-r from-blue-500 to-purple-500',
            'bg-gradient-to-r from-pink-500 to-red-500',
            'bg-gradient-to-r from-green-500 to-teal-500',
            'bg-gradient-to-r from-yellow-500 to-orange-500',
            'bg-gradient-to-r from-indigo-500 to-blue-500',
            'bg-gradient-to-r from-purple-500 to-pink-500',
            'bg-gradient-to-r from-cyan-500 to-blue-500',
            'bg-gradient-to-r from-emerald-500 to-green-500'
        ];

        // Generate consistent index based on UID
        let hash = 0;
        for (let i = 0; i < uid.length; i++) {
            hash = ((hash << 5) - hash + uid.charCodeAt(i)) & 0xffffffff;
        }
        
        return colors[Math.abs(hash) % colors.length];
    }

    /**
     * Update user preferences
     */
    async updatePreferences(preferences) {
        try {
            if (!this.userProfile) {
                throw new Error('User profile not loaded');
            }

            this.userProfile.preferences = {
                ...this.userProfile.preferences,
                ...preferences
            };

            console.log('✅ User preferences updated');
            
            // Emit preferences updated event
            this.emit('preferencesUpdated', this.userProfile.preferences);
            
            return this.userProfile.preferences;
            
        } catch (error) {
            console.error('❌ Failed to update preferences:', error);
            throw error;
        }
    }

    /**
     * Sign out user
     */
    async signOut() {
        try {
            // Set offline status
            await this.setPresenceStatus('offline');
            
            // Clear user data
            this.currentUser = null;
            this.userProfile = null;
            this.presenceStatus = 'offline';
            
            console.log('✅ User signed out');
            
            // Emit sign out event
            this.emit('userSignedOut');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to sign out user:', error);
            throw error;
        }
    }

    /**
     * Event listener management
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }

    /**
     * Convenience methods for event listeners
     */
    onProfileLoaded(callback) {
        this.on('profileLoaded', callback);
    }

    onProfileUpdated(callback) {
        this.on('profileUpdated', callback);
    }

    onPresenceChange(callback) {
        this.on('presenceChange', callback);
    }

    onPreferencesUpdated(callback) {
        this.on('preferencesUpdated', callback);
    }

    onUserSignedOut(callback) {
        this.on('userSignedOut', callback);
    }
}
