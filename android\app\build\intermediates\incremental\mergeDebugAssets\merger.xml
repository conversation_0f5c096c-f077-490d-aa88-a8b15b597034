<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\android\capacitor-cordova-android-plugins\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets"><file name="native-bridge.js" path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-toast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\toast\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-status-bar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\status-bar\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\splash-screen\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-share" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\share\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-push-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\push-notifications\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-preferences" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\preferences\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-network" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\network\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-local-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\local-notifications\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-keyboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\keyboard\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\haptics\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-filesystem" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\filesystem\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\device\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\node_modules\@capacitor\app\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/assets/auth.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\auth.js"/><file name="public/assets/components/RoomUI.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\components\RoomUI.js"/><file name="public/assets/config/firebase.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\config\firebase.js"/><file name="public/assets/dashboard.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\dashboard.js"/><file name="public/assets/room.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\room.js"/><file name="public/assets/services/RoomService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\services\RoomService.js"/><file name="public/assets/services/UserService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\services\UserService.js"/><file name="public/assets/services/VoiceService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\services\VoiceService.js"/><file name="public/assets/utils/helpers.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\utils\helpers.js"/><file name="public/cordova.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/dashboard.html" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\dashboard.html"/><file name="public/index.html" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\manifest.json"/><file name="public/room.html" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\room.html"/><file name="public/service-worker.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\service-worker.js"/><file name="public/assets/assets/auth.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\auth.js"/><file name="public/assets/assets/components/RoomUI.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\components\RoomUI.js"/><file name="public/assets/assets/config/firebase.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\config\firebase.js"/><file name="public/assets/assets/dashboard.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\dashboard.js"/><file name="public/assets/assets/room.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\room.js"/><file name="public/assets/assets/services/RoomService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\services\RoomService.js"/><file name="public/assets/assets/services/UserService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\services\UserService.js"/><file name="public/assets/assets/services/VoiceService.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\services\VoiceService.js"/><file name="public/assets/assets/utils/helpers.js" path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\main\assets\public\assets\assets\utils\helpers.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\AuraLink app\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>