/**
 * 🎯 Wavyn Room - Main Application Entry Point
 * Professional voice chat room implementation
 * Ready for mobile deployment with Capacitor
 */

import { FirebaseConfig } from './config/firebase.js';
import { RoomService } from './services/RoomService.js';
import { VoiceService } from './services/VoiceService.js';
import { UserService } from './services/UserService.js';
import { RoomUI } from './components/RoomUI.js';
import { URLHelper } from './utils/helpers.js';

class WavynRoom {
    constructor() {
        this.roomId = null;
        this.roomName = null;
        this.isInitialized = false;

        // Services
        this.firebaseConfig = null;
        this.roomService = null;
        this.voiceService = null;
        this.userService = null;
        this.ui = null;

        // Initialize the application
        this.init();
    }

    /**
     * Initialize the room application
     */
    async init() {
        try {
            console.log('🚀 Initializing Wavyn Room...');

            // Parse URL parameters
            const urlData = URLHelper.parseRoomURL();
            this.roomId = urlData.roomId;
            this.roomName = urlData.roomName;

            // Initialize Firebase
            this.firebaseConfig = new FirebaseConfig();
            await this.firebaseConfig.initialize();

            // Initialize services
            this.roomService = new RoomService(this.firebaseConfig.auth);
            this.voiceService = new VoiceService();
            this.userService = new UserService(this.firebaseConfig.auth);
            this.ui = new RoomUI();

            // Setup event listeners
            this.setupEventListeners();

            // Join the room
            await this.joinRoom();

            this.isInitialized = true;
            console.log('✅ Wavyn Room initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize room:', error);
            this.ui.showError('Failed to initialize room. Please try again.');
        }
    }

    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        // UI event listeners
        this.ui.onBackClick(() => this.handleBack());
        this.ui.onMuteToggle((isMuted) => this.handleMuteToggle(isMuted));
        this.ui.onLeaveRoom(() => this.handleLeaveRoom());
        this.ui.onSettingsClick(() => this.handleSettings());
        this.ui.onMenuToggle(() => this.handleMenuToggle());

        // Voice service events
        this.voiceService.onMuteChange((isMuted) => {
            this.ui.updateMuteStatus(isMuted);
        });

        // Room service events
        this.roomService.onMembersUpdate((members) => {
            this.ui.updateMembers(members);
        });

        this.roomService.onRoomUpdate((roomData) => {
            this.ui.updateRoomInfo(roomData);
        });
    }

    /**
     * Join the current room
     */
    async joinRoom() {
        try {
            this.ui.updateRoomStatus('Joining room...');

            // Join room via service
            const roomData = await this.roomService.joinRoom(this.roomId, this.roomName);

            // Update UI with room data
            this.ui.updateRoomInfo(roomData);
            this.ui.updateRoomStatus('Connected');

            // Initialize voice connection
            await this.voiceService.initialize();

            console.log('✅ Successfully joined room:', this.roomId);

        } catch (error) {
            console.error('❌ Failed to join room:', error);
            this.ui.updateRoomStatus('Connection failed');
            this.ui.showError('Failed to join room. Please check your connection.');
        }
    }

    /**
     * Handle back button click
     */
    handleBack() {
        if (confirm('Are you sure you want to leave this room?')) {
            this.leaveRoom();
        }
    }

    /**
     * Handle mute toggle
     */
    async handleMuteToggle(isMuted) {
        try {
            await this.voiceService.setMuted(isMuted);
            this.ui.updateMuteStatus(isMuted);
        } catch (error) {
            console.error('❌ Failed to toggle mute:', error);
            this.ui.showError('Failed to toggle microphone');
        }
    }

    /**
     * Handle leave room
     */
    async handleLeaveRoom() {
        if (confirm('Are you sure you want to leave this room?')) {
            await this.leaveRoom();
        }
    }

    /**
     * Handle settings click
     */
    handleSettings() {
        // TODO: Implement settings modal
        console.log('🔧 Settings clicked');
    }

    /**
     * Handle menu toggle
     */
    handleMenuToggle() {
        this.ui.toggleMenu();
    }

    /**
     * Leave the current room
     */
    async leaveRoom() {
        try {
            this.ui.updateRoomStatus('Leaving room...');

            // Leave room via service
            await this.roomService.leaveRoom(this.roomId);

            // Cleanup voice connection
            await this.voiceService.disconnect();

            // Navigate back to dashboard
            window.location.href = 'dashboard.html';

        } catch (error) {
            console.error('❌ Failed to leave room:', error);
            // Force navigation even if cleanup fails
            window.location.href = 'dashboard.html';
        }
    }
}

// Initialize the room when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.wavynRoom = new WavynRoom();
});