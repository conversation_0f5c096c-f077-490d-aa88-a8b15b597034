<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="Wavyn Dashboard - Join voice rooms and connect with your community">
    <title>Wavyn - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js"></script>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700;800;900&display=swap');
        
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            overflow-x: hidden;
        }
        
        /* Font families */
        .font-modern { font-family: 'Inter', sans-serif; }
        .font-cyber { font-family: 'Exo 2', 'Orbitron', sans-serif; }
        
        /* Background */
        .bg-minimal {
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 30%, #16213e 70%, #0f1419 100%);
        }
        
        /* Mobile-first styles */
        .mobile-btn {
            min-height: 56px;
            touch-action: manipulation;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        /* Room card hover effects */
        .room-card {
            transition: all 0.3s ease;
        }
        
        .room-card:hover {
            transform: translateY(-2px);
        }
        
        .room-card:active {
            transform: translateY(0);
        }
        
        /* Status indicators */
        .status-online {
            background: linear-gradient(45deg, #10b981, #34d399);
        }
        
        .status-busy {
            background: linear-gradient(45deg, #f59e0b, #fbbf24);
        }
        
        .status-offline {
            background: linear-gradient(45deg, #6b7280, #9ca3af);
        }
    </style>
</head>
<body class="bg-minimal min-h-screen font-modern">

    <!-- Header -->
    <header class="bg-gray-900/50 backdrop-blur-sm border-b border-gray-700 sticky top-0 z-40">
        <div class="flex items-center justify-between p-4">
            <!-- Logo -->
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-cyan-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span class="text-black font-bold text-sm">W</span>
                </div>
                <h1 class="text-xl font-bold text-white font-cyber">Wavyn</h1>
            </div>
            
            <!-- User Menu -->
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 status-online rounded-full"></div>
                    <span class="text-sm text-gray-300" id="userEmail">Loading...</span>
                </div>
                <button id="userMenuBtn" class="p-2 rounded-lg hover:bg-gray-700 transition-colors duration-200">
                    <svg class="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="p-4 pb-20">
        <!-- Welcome Section -->
        <section class="mb-8 animate-fadeInUp" style="animation-delay: 0.1s;">
            <h2 class="text-2xl font-bold text-white mb-2">Welcome back!</h2>
            <p class="text-gray-400">Join a voice room or create your own</p>
        </section>

        <!-- Quick Actions -->
        <section class="mb-8 animate-fadeInUp" style="animation-delay: 0.2s;">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <button id="createRoomBtn" class="mobile-btn bg-gradient-to-r from-cyan-500 to-pink-500 hover:from-cyan-400 hover:to-pink-400 active:from-cyan-600 active:to-pink-600 text-black font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform active:scale-95">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <span>Create Room</span>
                    </div>
                </button>
                
                <button id="joinRoomBtn" class="mobile-btn bg-gray-800 hover:bg-gray-700 active:bg-gray-900 text-white font-medium py-4 px-6 rounded-xl border border-gray-600 transition-all duration-200 transform active:scale-95">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                        </svg>
                        <span>Join with Code</span>
                    </div>
                </button>
            </div>
        </section>

        <!-- Active Rooms -->
        <section class="animate-fadeInUp" style="animation-delay: 0.3s;">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Active Rooms</h3>
                <button class="text-cyan-400 text-sm hover:text-cyan-300 transition-colors duration-200">
                    View All
                </button>
            </div>
            
            <div class="space-y-3" id="roomsList">
                <!-- Demo Rooms -->
                <div class="room-card bg-gray-800/50 border border-gray-700 rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <div class="w-3 h-3 status-online rounded-full"></div>
                                <h4 class="font-medium text-white">Gaming Lounge</h4>
                                <span class="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded-full">Public</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-2">Casual gaming and chat</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>👥 12 members</span>
                                <span>🎤 3 speaking</span>
                            </div>
                        </div>
                        <button class="mobile-btn bg-cyan-500 hover:bg-cyan-400 active:bg-cyan-600 text-black font-medium px-4 py-2 rounded-lg transition-all duration-200">
                            Join
                        </button>
                    </div>
                </div>

                <div class="room-card bg-gray-800/50 border border-gray-700 rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <div class="w-3 h-3 status-busy rounded-full"></div>
                                <h4 class="font-medium text-white">Study Session</h4>
                                <span class="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded-full">Private</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-2">Focus time with ambient sounds</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>👥 5 members</span>
                                <span>🔇 Silent mode</span>
                            </div>
                        </div>
                        <button class="mobile-btn bg-gray-600 hover:bg-gray-500 active:bg-gray-700 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200">
                            Request
                        </button>
                    </div>
                </div>

                <div class="room-card bg-gray-800/50 border border-gray-700 rounded-xl p-4 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <div class="w-3 h-3 status-online rounded-full"></div>
                                <h4 class="font-medium text-white">Music & Chill</h4>
                                <span class="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded-full">Public</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-2">Share your favorite tracks</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>👥 8 members</span>
                                <span>🎵 Music playing</span>
                            </div>
                        </div>
                        <button class="mobile-btn bg-cyan-500 hover:bg-cyan-400 active:bg-cyan-600 text-black font-medium px-4 py-2 rounded-lg transition-all duration-200">
                            Join
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 p-4">
        <div class="flex items-center justify-around">
            <button class="flex flex-col items-center space-y-1 text-cyan-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                </svg>
                <span class="text-xs">Rooms</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <span class="text-xs">Friends</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-xs">Settings</span>
            </button>
        </div>
    </nav>

    <!-- User Menu Dropdown -->
    <div id="userMenu" class="fixed top-16 right-4 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50 hidden">
        <div class="p-2">
            <button id="signOutBtn" class="w-full text-left px-4 py-2 text-red-400 hover:bg-gray-700 rounded-lg transition-colors duration-200">
                Sign Out
            </button>
        </div>
    </div>

    <script src="assets/dashboard.js"></script>
</body>
</html>
