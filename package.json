{"name": "wavyn-app", "version": "1.0.0", "description": "Wavyn - AI-powered voice chat app for creators, gamers, and teams", "main": "index.html", "scripts": {"dev": "npx http-server . -p 3000 -c-1", "build": "if not exist dist mkdir dist && copy *.html dist\\ && copy manifest.json dist\\ && copy service-worker.js dist\\ && xcopy assets dist\\assets\\ /E /I /Y", "capacitor:init": "npx cap init wavyn com.wavyn.app", "capacitor:add:android": "npx cap add android", "capacitor:add:ios": "npx cap add ios", "capacitor:sync": "npx cap sync", "capacitor:sync:android": "npx cap sync android", "capacitor:sync:ios": "npx cap sync ios", "capacitor:open:android": "npx cap open android", "capacitor:open:ios": "npx cap open ios", "capacitor:run:android": "npx cap run android", "capacitor:run:ios": "npx cap run ios", "android:build": "npx cap sync android && npx cap open android", "ios:build": "npx cap sync ios && npx cap open ios", "deploy:android": "npm run capacitor:sync:android && npm run capacitor:open:android", "deploy:ios": "npm run capacitor:sync:ios && npm run capacitor:open:ios"}, "keywords": ["voice-chat", "mobile-app", "capacitor", "pwa", "firebase", "webrtc", "gaming", "communication"], "author": "Wavyn Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wavyn/wavyn-app.git"}, "bugs": {"url": "https://github.com/wavyn/wavyn-app/issues"}, "homepage": "https://wavyn.app", "devDependencies": {"http-server": "^14.1.1"}, "dependencies": {"@capacitor/android": "^5.5.1", "@capacitor/app": "^5.0.6", "@capacitor/cli": "^5.5.1", "@capacitor/core": "^5.5.1", "@capacitor/device": "^5.0.6", "@capacitor/filesystem": "^5.1.4", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.5.1", "@capacitor/keyboard": "^5.0.6", "@capacitor/local-notifications": "^5.0.6", "@capacitor/network": "^5.0.6", "@capacitor/preferences": "^5.0.8", "@capacitor/push-notifications": "^5.1.0", "@capacitor/share": "^5.0.6", "@capacitor/splash-screen": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@capacitor/toast": "^5.0.6"}, "capacitor": {"appId": "com.wavyn.app", "appName": "<PERSON><PERSON><PERSON>", "webDir": "dist", "bundledWebRuntime": false}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}